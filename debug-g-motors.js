// Debug script to test G motor image paths
const { createImageObject } = require('./src/utils/imageUtils.js');

const basePath = '/GBPICS/Washing Machine spare pic/Motor/Wash Motor';
const gMotorFiles = [
  'Motor G19 suitable for LG 9 kg WASH.jpeg',
  'Motor wash G15 LG Top Load jpeg.jpeg',
  'Motor wash G15 alu.jpeg',
  'Motor wash G16 SS Top Load.jpeg',
  'Motor wash G16 alu.jpeg',
  'Motor wash G19 alu.jpeg',
  'Motor wash G20 alu.jpeg',
  'Motor wash G21 suitable for panasonic.jpeg'
];

console.log('=== G Motor Images Debug ===');
gMotorFiles.forEach(filename => {
  const imageObj = createImageObject(basePath, filename);
  console.log(`\nFilename: ${filename}`);
  console.log(`Primary Path: ${imageObj.path}`);
  console.log(`Alternative Path: ${imageObj.alternativePath}`);
  console.log(`Display Name: ${imageObj.name}`);
  console.log(`ID: ${imageObj.id}`);
});
