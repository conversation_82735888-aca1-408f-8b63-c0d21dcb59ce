<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G Motor Images Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .image-test { margin: 10px 0; display: flex; align-items: center; gap: 10px; }
        img { max-width: 150px; height: auto; border: 2px solid green; }
        img.error { border-color: red; }
        .path { font-family: monospace; background: #f5f5f5; padding: 5px; margin: 5px 0; font-size: 12px; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>G Motor Images Direct Test</h1>
    
    <div class="test-section">
        <h2>Testing Specific G Motor Images</h2>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor G19 suitable for LG 9 kg WASH.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20G19%20suitable%20for%20LG%209%20kg%20WASH.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20G19%20suitable%20for%20LG%209%20kg%20WASH.jpeg" 
                     alt="Motor G19" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G15 LG Top Load jpeg.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20LG%20Top%20Load%20jpeg.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20LG%20Top%20Load%20jpeg.jpeg" 
                     alt="Motor G15 LG" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G15 alu.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg" 
                     alt="Motor G15 alu" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G16 SS Top Load.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20SS%20Top%20Load.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20SS%20Top%20Load.jpeg" 
                     alt="Motor G16 SS" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G16 alu.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20alu.jpeg" 
                     alt="Motor G16 alu" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G19 alu.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G19%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G19%20alu.jpeg" 
                     alt="Motor G19 alu" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G20 alu.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg" 
                     alt="Motor G20 alu" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor wash G21 suitable for panasonic.jpeg</h3>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G21%20suitable%20for%20panasonic.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G21%20suitable%20for%20panasonic.jpeg" 
                     alt="Motor G21" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Alternative Path Tests</h2>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor G19 (Alternative Path)</h3>
                <div class="path">/gbpics-test/Motor/Wash%20Motor/Motor%20G19%20suitable%20for%20LG%209%20kg%20WASH.jpeg</div>
                <img src="/gbpics-test/Motor/Wash%20Motor/Motor%20G19%20suitable%20for%20LG%209%20kg%20WASH.jpeg" 
                     alt="Motor G19 alt" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <div class="image-test">
            <div style="flex: 1;">
                <h3>Motor G20 (Alternative Path)</h3>
                <div class="path">/gbpics-test/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg</div>
                <img src="/gbpics-test/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg" 
                     alt="Motor G20 alt" 
                     onerror="this.className='error'; this.nextSibling.innerHTML='FAILED'; this.nextSibling.className='status failed';"
                     onload="this.nextSibling.innerHTML='SUCCESS'; this.nextSibling.className='status success';">
                <span class="status">Loading...</span>
            </div>
        </div>
    </div>
</body>
</html>
