<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Specific G Motor Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-item { border: 1px solid #ccc; padding: 15px; border-radius: 8px; }
        .test-item h3 { margin-top: 0; color: #333; }
        .path-test { margin: 10px 0; }
        .path-test img { width: 100px; height: 100px; object-fit: cover; border: 2px solid #ddd; margin: 5px; }
        .path-test img.success { border-color: green; }
        .path-test img.error { border-color: red; }
        .path { font-family: monospace; font-size: 11px; background: #f5f5f5; padding: 3px; margin: 2px 0; word-break: break-all; }
        .status { font-weight: bold; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
        .success { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Specific G Motor Images Test</h1>
    <p>Testing the exact G motor images that are reported as not visible</p>
    
    <div class="test-grid">
        <!-- Motor wash G15 alu.jpeg -->
        <div class="test-item">
            <h3>Motor wash G15 alu.jpeg</h3>
            
            <div class="path-test">
                <strong>Path 1: Encoded spaces</strong>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
            
            <div class="path-test">
                <strong>Path 2: No encoding</strong>
                <div class="path">/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G15 alu.jpeg</div>
                <img src="/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G15 alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
            
            <div class="path-test">
                <strong>Path 3: Alternative (symlink)</strong>
                <div class="path">/gbpics-test/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg</div>
                <img src="/gbpics-test/Motor/Wash%20Motor/Motor%20wash%20G15%20alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <!-- Motor wash G16 alu.jpeg -->
        <div class="test-item">
            <h3>Motor wash G16 alu.jpeg</h3>
            
            <div class="path-test">
                <strong>Path 1: Encoded spaces</strong>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G16%20alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
            
            <div class="path-test">
                <strong>Path 2: No encoding</strong>
                <div class="path">/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G16 alu.jpeg</div>
                <img src="/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G16 alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <!-- Motor wash G19 alu.jpeg -->
        <div class="test-item">
            <h3>Motor wash G19 alu.jpeg</h3>
            
            <div class="path-test">
                <strong>Path 1: Encoded spaces</strong>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G19%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G19%20alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
            
            <div class="path-test">
                <strong>Path 2: No encoding</strong>
                <div class="path">/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G19 alu.jpeg</div>
                <img src="/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G19 alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
        </div>
        
        <!-- Motor wash G20 alu.jpeg -->
        <div class="test-item">
            <h3>Motor wash G20 alu.jpeg</h3>
            
            <div class="path-test">
                <strong>Path 1: Encoded spaces</strong>
                <div class="path">/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg</div>
                <img src="/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/Motor%20wash%20G20%20alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
            
            <div class="path-test">
                <strong>Path 2: No encoding</strong>
                <div class="path">/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G20 alu.jpeg</div>
                <img src="/GBPICS/Washing Machine spare pic/Motor/Wash Motor/Motor wash G20 alu.jpeg" 
                     onload="this.className='success'; this.nextSibling.innerHTML='✓ SUCCESS'; this.nextSibling.className='status success';"
                     onerror="this.className='error'; this.nextSibling.innerHTML='✗ FAILED'; this.nextSibling.className='status failed';">
                <span class="status">Loading...</span>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>Instructions:</h3>
        <ul>
            <li><strong>Green border + ✓ SUCCESS</strong> = Image loads correctly</li>
            <li><strong>Red border + ✗ FAILED</strong> = Image fails to load</li>
            <li>If all paths fail, the image file may be corrupted or missing</li>
            <li>If some paths work, we can use the working path format</li>
        </ul>
    </div>
</body>
</html>
