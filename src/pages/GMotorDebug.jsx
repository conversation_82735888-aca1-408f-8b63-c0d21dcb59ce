import { useState, useEffect } from 'react';
import { createImageObject } from '../utils/imageUtils';

export default function GMotorDebug() {
  const [debugInfo, setDebugInfo] = useState([]);

  useEffect(() => {
    const basePath = '/GBPICS/Washing Machine spare pic/Motor/Wash Motor';
    const gMotorFiles = [
      'Motor G19 suitable for LG 9 kg WASH.jpeg',
      'Motor wash G15 LG Top Load jpeg.jpeg',
      'Motor wash G15 alu.jpeg',
      'Motor wash G16 SS Top Load.jpeg',
      'Motor wash G16 alu.jpeg',
      'Motor wash G19 alu.jpeg',
      'Motor wash G20 alu.jpeg',
      'Motor wash G21 suitable for panasonic.jpeg'
    ];

    const debugData = gMotorFiles.map(filename => {
      const imageObj = createImageObject(basePath, filename);
      return {
        filename,
        imageObj,
        manualPath: `/GBPICS/Washing%20Machine%20spare%20pic/Motor/Wash%20Motor/${encodeURIComponent(filename)}`,
        simplePath: `/GBPICS/Washing Machine spare pic/Motor/Wash Motor/${filename}`
      };
    });

    setDebugInfo(debugData);
  }, []);

  const testImageLoad = async (path) => {
    try {
      const response = await fetch(path, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      return false;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-8">G Motor Images Debug</h1>
      
      <div className="space-y-6">
        {debugInfo.map((item, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md border">
            <h3 className="text-lg font-semibold mb-4 text-blue-600">{item.filename}</h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Generated Path (imageUtils):</h4>
                  <code className="text-xs bg-gray-100 p-2 rounded block break-all">
                    {item.imageObj.path}
                  </code>
                  <img 
                    src={item.imageObj.path} 
                    alt={item.filename}
                    className="mt-2 w-32 h-32 object-cover border-2 border-green-500"
                    onError={(e) => {
                      e.target.style.borderColor = 'red';
                      e.target.nextSibling.textContent = 'FAILED';
                      e.target.nextSibling.className = 'text-red-600 font-bold';
                    }}
                    onLoad={(e) => {
                      e.target.nextSibling.textContent = 'SUCCESS';
                      e.target.nextSibling.className = 'text-green-600 font-bold';
                    }}
                  />
                  <div className="text-gray-500">Loading...</div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Alternative Path:</h4>
                  <code className="text-xs bg-gray-100 p-2 rounded block break-all">
                    {item.imageObj.alternativePath}
                  </code>
                  <img 
                    src={item.imageObj.alternativePath} 
                    alt={item.filename}
                    className="mt-2 w-32 h-32 object-cover border-2 border-blue-500"
                    onError={(e) => {
                      e.target.style.borderColor = 'red';
                      e.target.nextSibling.textContent = 'FAILED';
                      e.target.nextSibling.className = 'text-red-600 font-bold';
                    }}
                    onLoad={(e) => {
                      e.target.nextSibling.textContent = 'SUCCESS';
                      e.target.nextSibling.className = 'text-green-600 font-bold';
                    }}
                  />
                  <div className="text-gray-500">Loading...</div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Manual Encoded Path:</h4>
                  <code className="text-xs bg-gray-100 p-2 rounded block break-all">
                    {item.manualPath}
                  </code>
                  <img 
                    src={item.manualPath} 
                    alt={item.filename}
                    className="mt-2 w-32 h-32 object-cover border-2 border-purple-500"
                    onError={(e) => {
                      e.target.style.borderColor = 'red';
                      e.target.nextSibling.textContent = 'FAILED';
                      e.target.nextSibling.className = 'text-red-600 font-bold';
                    }}
                    onLoad={(e) => {
                      e.target.nextSibling.textContent = 'SUCCESS';
                      e.target.nextSibling.className = 'text-green-600 font-bold';
                    }}
                  />
                  <div className="text-gray-500">Loading...</div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Simple Path (no encoding):</h4>
                  <code className="text-xs bg-gray-100 p-2 rounded block break-all">
                    {item.simplePath}
                  </code>
                  <img 
                    src={item.simplePath} 
                    alt={item.filename}
                    className="mt-2 w-32 h-32 object-cover border-2 border-orange-500"
                    onError={(e) => {
                      e.target.style.borderColor = 'red';
                      e.target.nextSibling.textContent = 'FAILED';
                      e.target.nextSibling.className = 'text-red-600 font-bold';
                    }}
                    onLoad={(e) => {
                      e.target.nextSibling.textContent = 'SUCCESS';
                      e.target.nextSibling.className = 'text-green-600 font-bold';
                    }}
                  />
                  <div className="text-gray-500">Loading...</div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-gray-50 rounded">
              <h4 className="font-medium text-gray-700 mb-2">Image Object Details:</h4>
              <pre className="text-xs overflow-x-auto">
                {JSON.stringify(item.imageObj, null, 2)}
              </pre>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
